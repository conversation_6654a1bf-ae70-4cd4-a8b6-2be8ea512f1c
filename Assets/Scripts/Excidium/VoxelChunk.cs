using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Excidium
{
    /// <summary>
    /// Optimized voxel chunk system with face culling and efficient mesh generation
    /// </summary>
    public class VoxelChunk : MonoBehaviour
{
    [Header("Chunk Settings")] public int chunkSize = 16;
    public float noiseScale = 0.1f;
    public float heightMultiplier = 10f;
    public Material chunkMaterial;

    [Header("Debug")] public bool showDebugInfo;

    [Header("Editor Visualization")] public bool showChunkBounds = true;
    public bool showVoxelGrid = false;
    [Range(0.1f, 1.0f)] public float gizmoAlpha = 0.3f;

    // Voxel data: true = solid, false = air
    private bool[,,] _voxels;

    // Mesh components
    private MeshFilter _meshFilter;
    private MeshRenderer _meshRenderer;
    private MeshCollider _meshCollider;

    // Mesh generation data
    private readonly List<Vector3> _vertices = new();
    private readonly List<int> _triangles = new();
    private readonly List<Vector2> _uvs = new();

    // Face directions for culling
    private static readonly Vector3Int[] FaceDirections =
    {
        Vector3Int.up, // Top
        Vector3Int.down, // Bottom
        Vector3Int.forward, // Front
        Vector3Int.back, // Back
        Vector3Int.right, // Right
        Vector3Int.left // Left
    };

    void Start()
    {
        Initialize();
    }

    /// <summary>
    /// Public method to initialize the chunk (useful for testing)
    /// </summary>
    public void Initialize()
    {
        InitializeComponents();
        InitializeVoxelArray();
        GenerateTerrain();
        GenerateMesh();
    }

    private void InitializeComponents()
    {
        _meshFilter = gameObject.AddComponent<MeshFilter>();
        _meshRenderer = gameObject.AddComponent<MeshRenderer>();
        _meshCollider = gameObject.AddComponent<MeshCollider>();

        if (chunkMaterial != null)
            _meshRenderer.material = chunkMaterial;
    }

    private void OnDestroy()
    {
        if (_meshFilter != null)
            Destroy(_meshFilter);

        if (_meshRenderer != null)
            Destroy(_meshRenderer);

        if (_meshCollider != null)
            Destroy(_meshCollider);
    }

    private void InitializeVoxelArray()
    {
        _voxels = new bool[chunkSize, chunkSize, chunkSize];
    }

    private void GenerateTerrain()
    {
        var worldPos = transform.position;

        for (var x = 0; x < chunkSize; x++)
        {
            for (var z = 0; z < chunkSize; z++)
            {
                // Generate height using Perlin noise
                var noiseValue = Mathf.PerlinNoise(
                    (x + worldPos.x) * noiseScale,
                    (z + worldPos.z) * noiseScale
                );

                var height = Mathf.FloorToInt(noiseValue * heightMultiplier);

                // Fill voxels up to the height
                for (var y = 0; y < chunkSize; y++)
                {
                    _voxels[x, y, z] = y <= height;
                }
            }
        }

        if (showDebugInfo)
            Debug.Log($"Generated terrain for chunk at {transform.position}");
    }

    private void GenerateMesh()
    {
        // Clear previous mesh data
        _vertices.Clear();
        _triangles.Clear();
        _uvs.Clear();

        // Generate mesh with face culling
        for (var x = 0; x < chunkSize; x++)
        {
            for (var y = 0; y < chunkSize; y++)
            {
                for (var z = 0; z < chunkSize; z++)
                {
                    if (IsVoxelSolid(x, y, z))
                    {
                        GenerateVoxelFaces(x, y, z);
                    }
                }
            }
        }

        // Create and assign mesh
        CreateAndAssignMesh();

        if (showDebugInfo)
            Debug.Log(
                $"Generated mesh with {_vertices.Count} vertices and {_triangles.Count / 3} triangles");
    }

    private bool IsVoxelSolid(int x, int y, int z)
    {
        // Check bounds
        if (x < 0 || x >= chunkSize || y < 0 || y >= chunkSize || z < 0 || z >= chunkSize)
            return false;

        return _voxels[x, y, z];
    }

    private void GenerateVoxelFaces(int x, int y, int z)
    {
        var voxelPosition = new Vector3(x, y, z);

        // Check each face direction and only generate faces that are exposed
        for (var i = 0; i < FaceDirections.Length; i++)
        {
            var direction = FaceDirections[i];
            var neighborPos = new Vector3Int(x, y, z) + direction;

            // If neighbor is air or outside chunk bounds, generate this face
            if (!IsVoxelSolid(neighborPos.x, neighborPos.y, neighborPos.z))
            {
                GenerateFace(voxelPosition, i);
            }
        }
    }

    private void GenerateFace(Vector3 voxelPos, int faceIndex)
    {
        var vertexIndex = _vertices.Count;

        // Define face vertices based on face direction
        var faceVertices = GetFaceVertices(voxelPos, faceIndex);

        _vertices.AddRange(faceVertices);

        // Add UVs for this face (matching vertex order: BL, TL, TR, BR)
        _uvs.AddRange(new Vector2[]
        {
            new(0, 0), // Bottom-left
            new(0, 1), // Top-left
            new(1, 1), // Top-right
            new(1, 0) // Bottom-right
        });

        // Add triangles with correct winding order (counter-clockwise)
        // Triangle 1: BL -> TL -> TR (0 -> 1 -> 2)
        // Triangle 2: BL -> TR -> BR (0 -> 2 -> 3)
        _triangles.AddRange(new[]
        {
            vertexIndex + 0, vertexIndex + 1, vertexIndex + 2, // First triangle
            vertexIndex + 0, vertexIndex + 2, vertexIndex + 3 // Second triangle
        });
    }

    private static Vector3[] GetFaceVertices(Vector3 voxelPos, int faceIndex)
    {
        // Define vertices for each face with correct winding order (counter-clockwise when viewed from outside)
        // Each face is defined as a quad with vertices in counter-clockwise order for proper normals
        return faceIndex switch
        {
            0 => new[]
            {
                // Top (+Y) - looking down at the face
                voxelPos + new Vector3(0, 1, 0), // Bottom-left
                voxelPos + new Vector3(0, 1, 1), // Top-left
                voxelPos + new Vector3(1, 1, 1), // Top-right
                voxelPos + new Vector3(1, 1, 0) // Bottom-right
            },
            1 => new[]
            {
                // Bottom (-Y) - looking up at the face
                voxelPos + new Vector3(0, 0, 0), // Bottom-left
                voxelPos + new Vector3(1, 0, 0), // Bottom-right
                voxelPos + new Vector3(1, 0, 1), // Top-right
                voxelPos + new Vector3(0, 0, 1) // Top-left
            },
            2 => new[]
            {
                // Front (+Z) - looking at the face from negative Z
                voxelPos + new Vector3(0, 0, 1), // Bottom-left
                voxelPos + new Vector3(0, 1, 1), // Top-left
                voxelPos + new Vector3(1, 1, 1), // Top-right
                voxelPos + new Vector3(1, 0, 1) // Bottom-right
            },
            3 => new[]
            {
                // Back (-Z) - looking at the face from positive Z
                voxelPos + new Vector3(1, 0, 0), // Bottom-left
                voxelPos + new Vector3(1, 1, 0), // Top-left
                voxelPos + new Vector3(0, 1, 0), // Top-right
                voxelPos + new Vector3(0, 0, 0) // Bottom-right
            },
            4 => new[]
            {
                // Right (+X) - looking at the face from negative X
                voxelPos + new Vector3(1, 0, 1), // Bottom-left
                voxelPos + new Vector3(1, 1, 1), // Top-left
                voxelPos + new Vector3(1, 1, 0), // Top-right
                voxelPos + new Vector3(1, 0, 0) // Bottom-right
            },
            5 => new[]
            {
                // Left (-X) - looking at the face from positive X
                voxelPos + new Vector3(0, 0, 0), // Bottom-left
                voxelPos + new Vector3(0, 1, 0), // Top-left
                voxelPos + new Vector3(0, 1, 1), // Top-right
                voxelPos + new Vector3(0, 0, 1) // Bottom-right
            },
            _ => new Vector3[4]
        };
    }

    private void CreateAndAssignMesh()
    {
        var mesh = new Mesh();

        // Assign mesh data
        mesh.vertices = _vertices.ToArray();
        mesh.triangles = _triangles.ToArray();
        mesh.uv = _uvs.ToArray();

        // Calculate normals and optimize
        mesh.RecalculateNormals();
        mesh.RecalculateBounds();
        mesh.Optimize();

        // Assign to components
        _meshFilter.mesh = mesh;
        _meshCollider.sharedMesh = mesh;

        // Validate mesh for debugging
        if (showDebugInfo)
        {
            ValidateMesh(mesh);
        }
    }

    private void ValidateMesh(Mesh mesh)
    {
        var vertices = mesh.vertices;
        var triangles = mesh.triangles;
        var normals = mesh.normals;

        Debug.Log(
            $"Mesh validation - Vertices: {vertices.Length}, Triangles: {triangles.Length / 3}, Normals: {normals.Length}");

        // Check for degenerate triangles and winding order
        var degenerateCount = 0;
        var wrongWindingCount = 0;

        for (var i = 0; i < triangles.Length; i += 3)
        {
            var v0 = vertices[triangles[i]];
            var v1 = vertices[triangles[i + 1]];
            var v2 = vertices[triangles[i + 2]];

            // Check triangle area (degenerate check)
            var calculatedNormal = Vector3.Cross(v1 - v0, v2 - v0);
            var area = calculatedNormal.magnitude;
            if (area < 0.001f)
            {
                degenerateCount++;
                continue;
            }

            // Check winding order by comparing calculated normal with mesh normal
            calculatedNormal = calculatedNormal.normalized;
            var meshNormal = normals[triangles[i]];
            var dot = Vector3.Dot(calculatedNormal, meshNormal);

            if (dot < 0.5f) // Threshold for "wrong" orientation
            {
                wrongWindingCount++;
            }
        }

        if (degenerateCount > 0)
        {
            Debug.LogWarning($"Found {degenerateCount} degenerate triangles in mesh");
        }

        if (wrongWindingCount > 0)
        {
            Debug.LogWarning(
                $"Found {wrongWindingCount} triangles with potentially incorrect winding order");
        }

        Debug.Log($"Mesh bounds: {mesh.bounds}");
        Debug.Log(
            $"Validation complete - Degenerate: {degenerateCount}, Wrong winding: {wrongWindingCount}");
    }

    /// <summary>
    /// Public method to regenerate the chunk (useful for runtime modifications)
    /// </summary>
    public void RegenerateChunk()
    {
        GenerateTerrain();
        GenerateMesh();
    }

    /// <summary>
    /// Public method to regenerate only the mesh without changing terrain (useful for voxel modifications)
    /// </summary>
    public void RegenerateMesh()
    {
        GenerateMesh();
    }

    /// <summary>
    /// Get voxel state at position (useful for external systems)
    /// </summary>
    public bool GetVoxel(int x, int y, int z)
    {
        return IsVoxelSolid(x, y, z);
    }

    /// <summary>
    /// Set voxel state at position (useful for runtime modifications)
    /// </summary>
    public void SetVoxel(int x, int y, int z, bool solid)
    {
        if (x >= 0 && x < chunkSize && y >= 0 && y < chunkSize && z >= 0 && z < chunkSize)
        {
            _voxels[x, y, z] = solid;
        }
    }

#if UNITY_EDITOR
    /// <summary>
    /// Draw gizmos in the editor to visualize the chunk
    /// </summary>
    private void OnDrawGizmos()
    {
        if (!showChunkBounds && !showVoxelGrid) return;

        var originalColor = Gizmos.color;
        var chunkPosition = transform.position;

        if (showChunkBounds)
        {
            DrawChunkBounds(chunkPosition);
        }

        if (showVoxelGrid)
        {
            DrawVoxelGrid(chunkPosition);
        }

        Gizmos.color = originalColor;
    }

    private void DrawChunkBounds(Vector3 chunkPosition)
    {
        // Draw chunk boundary as a wireframe cube
        Gizmos.color = new Color(1f, 1f, 1f, gizmoAlpha);
        var center = chunkPosition + Vector3.one * (chunkSize * 0.5f);
        var size = Vector3.one * chunkSize;

        Gizmos.DrawWireCube(center, size);

        // Draw a slightly transparent filled cube for better visibility
        Gizmos.color = new Color(1f, 1f, 1f, gizmoAlpha * 0.1f);
        Gizmos.DrawCube(center, size);
    }

    private void DrawVoxelGrid(Vector3 chunkPosition)
    {
        // Draw individual voxel boundaries (only when voxels exist or in edit mode)
        Gizmos.color = new Color(0.8f, 0.8f, 0.8f, gizmoAlpha * 0.5f);

        if (Application.isPlaying && _voxels != null)
        {
            // Runtime: Draw only solid voxels
            DrawRuntimeVoxels(chunkPosition);
        }
        else
        {
            // Edit time: Draw grid pattern for visualization
            DrawEditTimeGrid(chunkPosition);
        }
    }

    private void DrawRuntimeVoxels(Vector3 chunkPosition)
    {
        for (var x = 0; x < chunkSize; x++)
        {
            for (var y = 0; y < chunkSize; y++)
            {
                for (var z = 0; z < chunkSize; z++)
                {
                    if (_voxels[x, y, z])
                    {
                        var voxelCenter =
                            chunkPosition + new Vector3(x + 0.5f, y + 0.5f, z + 0.5f);
                        Gizmos.DrawWireCube(voxelCenter, Vector3.one);
                    }
                }
            }
        }
    }

    private void DrawEditTimeGrid(Vector3 chunkPosition)
    {
        // Draw a subset of the grid to avoid performance issues
        var step = Mathf.Max(1, chunkSize / 8); // Show every nth voxel

        for (var x = 0; x < chunkSize; x += step)
        {
            for (var y = 0; y < chunkSize; y += step)
            {
                for (var z = 0; z < chunkSize; z += step)
                {
                    var voxelCenter = chunkPosition + new Vector3(x + 0.5f, y + 0.5f, z + 0.5f);
                    Gizmos.DrawWireCube(voxelCenter, Vector3.one * 0.8f);
                }
            }
        }
    }

    /// <summary>
    /// Draw gizmos when selected (more detailed view)
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        if (!showChunkBounds && !showVoxelGrid) return;

        var originalColor = Gizmos.color;
        var chunkPosition = transform.position;

        // Draw coordinate axes
        DrawCoordinateAxes(chunkPosition);

        // Draw chunk info
        DrawChunkInfo(chunkPosition);

        Gizmos.color = originalColor;
    }

    private void DrawCoordinateAxes(Vector3 chunkPosition)
    {
        var axisLength = chunkSize * 0.3f;

        // X-axis (Red)
        Gizmos.color = Color.red;
        Gizmos.DrawLine(chunkPosition, chunkPosition + Vector3.right * axisLength);

        // Y-axis (Green)
        Gizmos.color = Color.green;
        Gizmos.DrawLine(chunkPosition, chunkPosition + Vector3.up * axisLength);

        // Z-axis (Blue)
        Gizmos.color = Color.blue;
        Gizmos.DrawLine(chunkPosition, chunkPosition + Vector3.forward * axisLength);
    }

    private void DrawChunkInfo(Vector3 chunkPosition)
    {
        // Draw chunk size label
        var labelPosition = chunkPosition + Vector3.up * (chunkSize + 1);

#if UNITY_EDITOR
        var style = new GUIStyle();
        style.normal.textColor = Color.white;
        style.fontSize = 12;

        Handles.Label(labelPosition, $"Chunk Size: {chunkSize}x{chunkSize}x{chunkSize}", style);

        if (Application.isPlaying && _voxels != null)
        {
            var solidCount = CountSolidVoxels();
            Handles.Label(labelPosition + Vector3.down * 0.5f, $"Solid Voxels: {solidCount}",
                style);
        }
#endif
    }

    private int CountSolidVoxels()
    {
        var count = 0;
        for (var x = 0; x < chunkSize; x++)
        {
            for (var y = 0; y < chunkSize; y++)
            {
                for (var z = 0; z < chunkSize; z++)
                {
                    if (_voxels[x, y, z]) count++;
                }
            }
        }

        return count;
    }
#endif
    }
}