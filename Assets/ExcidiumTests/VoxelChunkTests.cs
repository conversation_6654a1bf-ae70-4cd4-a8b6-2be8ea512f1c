using System;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Object = UnityEngine.Object;
using Excidium;

namespace ExcidiumTests
{
    /// <summary>
    /// Comprehensive test suite for VoxelChunk component
    /// Tests face rendering, mesh generation, and prevents regressions
    /// </summary>
    public class VoxelChunkTests
    {
        private GameObject _testGameObject;
        private VoxelChunk _voxelChunk;
        private const int TestChunkSize = 4; // Small size for faster tests
        private const float FloatTolerance = 0.001f;

        [SetUp]
        public void SetUp()
        {
            // Create test GameObject with VoxelChunk component
            _testGameObject = new GameObject("TestVoxelChunk");
            _voxelChunk = _testGameObject.AddComponent<VoxelChunk>();
            
            // Configure for testing
            _voxelChunk.chunkSize = TestChunkSize;
            _voxelChunk.showDebugInfo = false; // Reduce console spam during tests
            _voxelChunk.noiseScale = 0.1f;
            _voxelChunk.heightMultiplier = 2;
        }

        [TearDown]
        public void TearDown()
        {
            if (_testGameObject != null)
            {
                Object.DestroyImmediate(_testGameObject);
            }
        }

        #region Initialization Tests

        [Test]
        public void VoxelChunk_WhenCreated_ShouldHaveRequiredComponents()
        {
            // Act
            _voxelChunk.Initialize();

            // Assert
            Assert.IsNotNull(_voxelChunk.GetComponent<MeshFilter>(), "MeshFilter should be added");
            Assert.IsNotNull(_voxelChunk.GetComponent<MeshRenderer>(), "MeshRenderer should be added");
            Assert.IsNotNull(_voxelChunk.GetComponent<MeshCollider>(), "MeshCollider should be added");
        }

        [Test]
        public void VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray()
        {
            // Act
            _voxelChunk.Initialize();

            // Assert - Test through public interface
            bool hasVoxels = false;
            for (int x = 0; x < TestChunkSize && !hasVoxels; x++)
            {
                for (int y = 0; y < TestChunkSize && !hasVoxels; y++)
                {
                    for (int z = 0; z < TestChunkSize && !hasVoxels; z++)
                    {
                        // At least some voxels should be solid due to terrain generation
                        if (_voxelChunk.GetVoxel(x, y, z))
                        {
                            hasVoxels = true;
                        }
                    }
                }
            }
            Assert.IsTrue(hasVoxels, "Terrain generation should create some solid voxels");
        }

        #endregion

        #region Voxel Management Tests

        [Test]
        public void GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue()
        {
            // Arrange
            _voxelChunk.Initialize();

            // Act & Assert - Test bounds
            Assert.IsFalse(_voxelChunk.GetVoxel(-1, 0, 0), "Should return false for negative X");
            Assert.IsFalse(_voxelChunk.GetVoxel(0, -1, 0), "Should return false for negative Y");
            Assert.IsFalse(_voxelChunk.GetVoxel(0, 0, -1), "Should return false for negative Z");
            Assert.IsFalse(_voxelChunk.GetVoxel(TestChunkSize, 0, 0), "Should return false for X >= chunkSize");
            Assert.IsFalse(_voxelChunk.GetVoxel(0, TestChunkSize, 0), "Should return false for Y >= chunkSize");
            Assert.IsFalse(_voxelChunk.GetVoxel(0, 0, TestChunkSize), "Should return false for Z >= chunkSize");
        }

        [Test]
        public void SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState()
        {
            // Arrange
            _voxelChunk.Initialize();
            const int x = 1, y = 1, z = 1;

            // Act
            _voxelChunk.SetVoxel(x, y, z, true);

            // Assert
            Assert.IsTrue(_voxelChunk.GetVoxel(x, y, z), "Voxel should be set to solid");

            // Act
            _voxelChunk.SetVoxel(x, y, z, false);

            // Assert
            Assert.IsFalse(_voxelChunk.GetVoxel(x, y, z), "Voxel should be set to air");
        }

        [Test]
        public void SetVoxel_WithInvalidCoordinates_ShouldNotCrash()
        {
            // Arrange
            _voxelChunk.Initialize();

            // Act & Assert - Should not throw exceptions
            Assert.DoesNotThrow(() => _voxelChunk.SetVoxel(-1, 0, 0, true));
            Assert.DoesNotThrow(() => _voxelChunk.SetVoxel(TestChunkSize, 0, 0, true));
            Assert.DoesNotThrow(() => _voxelChunk.SetVoxel(0, -1, 0, true));
            Assert.DoesNotThrow(() => _voxelChunk.SetVoxel(0, TestChunkSize, 0, true));
            Assert.DoesNotThrow(() => _voxelChunk.SetVoxel(0, 0, -1, true));
            Assert.DoesNotThrow(() => _voxelChunk.SetVoxel(0, 0, TestChunkSize, true));
        }

        #endregion

        #region Mesh Generation Tests

        [Test]
        public void MeshGeneration_WhenCalled_ShouldCreateValidMesh()
        {
            // Arrange
            _voxelChunk.Initialize();

            // Act
            var meshFilter = _voxelChunk.GetComponent<MeshFilter>();
            var mesh = meshFilter.mesh;

            // Assert
            Assert.IsNotNull(mesh, "Mesh should be created");
            Assert.Greater(mesh.vertices.Length, 0, "Mesh should have vertices");
            Assert.Greater(mesh.triangles.Length, 0, "Mesh should have triangles");
            Assert.AreEqual(mesh.vertices.Length, mesh.uv.Length, "UV count should match vertex count");
            Assert.IsTrue(mesh.triangles.Length % 3 == 0, "Triangle count should be divisible by 3");
        }

        [Test]
        public void MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount()
        {
            // Arrange - Create chunk with only one voxel
            _voxelChunk.Initialize();
            
            // Clear all voxels first
            for (int x = 0; x < TestChunkSize; x++)
            {
                for (int y = 0; y < TestChunkSize; y++)
                {
                    for (int z = 0; z < TestChunkSize; z++)
                    {
                        _voxelChunk.SetVoxel(x, y, z, false);
                    }
                }
            }
            
            // Set only one voxel
            _voxelChunk.SetVoxel(1, 1, 1, true);

            // Act
            _voxelChunk.RegenerateChunk();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().mesh;

            // Assert - Single isolated voxel should have 6 faces (24 vertices, 12 triangles)
            Assert.AreEqual(24, mesh.vertices.Length, "Single voxel should have 24 vertices (6 faces � 4 vertices)");
            Assert.AreEqual(36, mesh.triangles.Length, "Single voxel should have 36 triangle indices (6 faces � 2 triangles � 3 indices)");
        }

        #endregion

        #region Face Orientation Tests

        [Test]
        public void FaceGeneration_ShouldHaveCorrectWindingOrder()
        {
            // Arrange - Create a single voxel for testing
            _voxelChunk.Initialize();
            
            // Clear all voxels
            for (int x = 0; x < TestChunkSize; x++)
            {
                for (int y = 0; y < TestChunkSize; y++)
                {
                    for (int z = 0; z < TestChunkSize; z++)
                    {
                        _voxelChunk.SetVoxel(x, y, z, false);
                    }
                }
            }
            
            // Set single voxel at (1,1,1)
            _voxelChunk.SetVoxel(1, 1, 1, true);

            // Act
            _voxelChunk.RegenerateChunk();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().mesh;

            // Assert - Check that normals point outward
            var vertices = mesh.vertices;
            var triangles = mesh.triangles;
            var normals = mesh.normals;

            for (int i = 0; i < triangles.Length; i += 3)
            {
                var v0 = vertices[triangles[i]];
                var v1 = vertices[triangles[i + 1]];
                var v2 = vertices[triangles[i + 2]];

                // Calculate face normal using cross product
                var calculatedNormal = Vector3.Cross(v1 - v0, v2 - v0).normalized;
                var meshNormal = normals[triangles[i]];

                // Normals should be consistent (dot product should be positive)
                var dot = Vector3.Dot(calculatedNormal, meshNormal);
                Assert.Greater(dot, 0.5f, $"Triangle {i / 3} has incorrect winding order. Dot product: {dot}");
            }
        }

        [Test]
        public void TopFace_ShouldPointUpward()
        {
            // Arrange - Create voxel with exposed top face
            _voxelChunk.Initialize();
            
            // Clear all voxels
            for (int x = 0; x < TestChunkSize; x++)
            {
                for (int y = 0; y < TestChunkSize; y++)
                {
                    for (int z = 0; z < TestChunkSize; z++)
                    {
                        _voxelChunk.SetVoxel(x, y, z, false);
                    }
                }
            }
            
            // Set voxel at bottom to ensure top face is exposed
            _voxelChunk.SetVoxel(1, 0, 1, true);

            // Act
            _voxelChunk.RegenerateChunk();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().mesh;

            // Assert - Find top face and verify it points upward
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            
            bool foundTopFace = false;
            for (int i = 0; i < vertices.Length; i++)
            {
                var vertex = vertices[i];
                var normal = normals[i];
                
                // Check if this is a top face vertex (Y = 1 for our voxel at Y=0)
                if (Mathf.Abs(vertex.y - 1.0f) < FloatTolerance)
                {
                    // Top face normal should point upward (positive Y)
                    Assert.Greater(normal.y, 0.8f, $"Top face normal should point upward. Normal: {normal}");
                    foundTopFace = true;
                }
            }
            
            Assert.IsTrue(foundTopFace, "Should find at least one top face vertex");
        }

        #endregion

        #region Face Culling Tests

        [Test]
        public void FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces()
        {
            // Arrange - Create two adjacent voxels
            _voxelChunk.Initialize();
            
            // Clear all voxels
            for (int x = 0; x < TestChunkSize; x++)
            {
                for (int y = 0; y < TestChunkSize; y++)
                {
                    for (int z = 0; z < TestChunkSize; z++)
                    {
                        _voxelChunk.SetVoxel(x, y, z, false);
                    }
                }
            }
            
            // Set two adjacent voxels
            _voxelChunk.SetVoxel(1, 1, 1, true);
            _voxelChunk.SetVoxel(2, 1, 1, true); // Adjacent in X direction

            // Act
            _voxelChunk.RegenerateChunk();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().mesh;

            // Assert - Two adjacent voxels should have fewer faces than two isolated voxels
            // Each voxel loses one face where they touch, so: (6 + 6) - 2 = 10 faces total
            var expectedVertices = 10 * 4; // 10 faces � 4 vertices per face
            var expectedTriangles = 10 * 6; // 10 faces � 2 triangles � 3 indices per triangle
            
            Assert.AreEqual(expectedVertices, mesh.vertices.Length, 
                $"Adjacent voxels should have {expectedVertices} vertices (10 faces), got {mesh.vertices.Length}");
            Assert.AreEqual(expectedTriangles, mesh.triangles.Length,
                $"Adjacent voxels should have {expectedTriangles} triangle indices, got {mesh.triangles.Length}");
        }

        #endregion

        #region Regression Prevention Tests

        [Test]
        public void RegenerateChunk_ShouldProduceSameResultAsInitialGeneration()
        {
            // Arrange
            _voxelChunk.Initialize();
            var initialMesh = _voxelChunk.GetComponent<MeshFilter>().mesh;
            var initialVertexCount = initialMesh.vertices.Length;
            var initialTriangleCount = initialMesh.triangles.Length;

            // Act
            _voxelChunk.RegenerateChunk();
            var regeneratedMesh = _voxelChunk.GetComponent<MeshFilter>().mesh;

            // Assert
            Assert.AreEqual(initialVertexCount, regeneratedMesh.vertices.Length, 
                "Regenerated mesh should have same vertex count");
            Assert.AreEqual(initialTriangleCount, regeneratedMesh.triangles.Length,
                "Regenerated mesh should have same triangle count");
        }

        [Test]
        public void MeshBounds_ShouldMatchChunkSize()
        {
            // Arrange & Act
            _voxelChunk.Initialize();
            var mesh = _voxelChunk.GetComponent<MeshFilter>().mesh;
            var bounds = mesh.bounds;

            // Assert - Bounds should encompass the entire chunk
            Assert.LessOrEqual(bounds.min.x, 0, "Bounds should include chunk origin X");
            Assert.LessOrEqual(bounds.min.y, 0, "Bounds should include chunk origin Y");
            Assert.LessOrEqual(bounds.min.z, 0, "Bounds should include chunk origin Z");
            Assert.GreaterOrEqual(bounds.max.x, TestChunkSize, "Bounds should include chunk max X");
            Assert.GreaterOrEqual(bounds.max.y, TestChunkSize, "Bounds should include chunk max Y");
            Assert.GreaterOrEqual(bounds.max.z, TestChunkSize, "Bounds should include chunk max Z");
        }

        [Test]
        public void VoxelChunk_ShouldNotHaveMemoryLeaks()
        {
            // Arrange
            var initialMeshCount = Resources.FindObjectsOfTypeAll<Mesh>().Length;

            // Act - Create and destroy multiple chunks
            for (int i = 0; i < 5; i++)
            {
                var tempGO = new GameObject($"TempChunk{i}");
                var tempChunk = tempGO.AddComponent<VoxelChunk>();
                tempChunk.chunkSize = 2; // Very small for speed
                tempChunk.Initialize();
                Object.DestroyImmediate(tempGO);
            }

            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();

            // Assert - Should not have significantly more meshes
            var finalMeshCount = Resources.FindObjectsOfTypeAll<Mesh>().Length;
            var meshDifference = finalMeshCount - initialMeshCount;
            
            Assert.LessOrEqual(meshDifference, 2, 
                $"Should not leak meshes. Initial: {initialMeshCount}, Final: {finalMeshCount}, Difference: {meshDifference}");
        }

        #endregion
    }
}
