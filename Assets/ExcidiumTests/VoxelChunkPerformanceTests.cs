using System.Collections;
using System.Diagnostics;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using UnityEngine.Profiling;
using Excidium;

namespace ExcidiumTests
{
    /// <summary>
    /// Performance and benchmark tests for VoxelChunk
    /// Ensures the component maintains acceptable performance characteristics
    /// </summary>
    public class VoxelChunkPerformanceTests
    {
        private GameObject _testGameObject;
        private VoxelChunk _voxelChunk;

        [SetUp]
        public void SetUp()
        {
            _testGameObject = new GameObject("PerformanceTestChunk");
            _voxelChunk = _testGameObject.AddComponent<VoxelChunk>();
            _voxelChunk.showDebugInfo = false; // Disable debug to avoid affecting performance
        }

        [TearDown]
        public void TearDown()
        {
            if (_testGameObject != null)
            {
                Object.DestroyImmediate(_testGameObject);
            }
        }

        #region Generation Performance Tests

        [UnityTest]
        public IEnumerator SmallChunk_ShouldGenerateQuickly()
        {
            // Arrange
            _voxelChunk.chunkSize = 8;
            var stopwatch = Stopwatch.StartNew();

            // Act
            yield return null; // Wait for generation

            // Assert
            stopwatch.Stop();
            var generationTime = stopwatch.ElapsedMilliseconds;
            
            Assert.Less(generationTime, 50, $"Small chunk (8�) took too long to generate: {generationTime}ms");
            UnityEngine.Debug.Log($"Small chunk generation time: {generationTime}ms");
        }

        [UnityTest]
        public IEnumerator MediumChunk_ShouldGenerateWithinBudget()
        {
            // Arrange
            _voxelChunk.chunkSize = 16;
            var stopwatch = Stopwatch.StartNew();

            // Act
            yield return null; // Wait for generation

            // Assert
            stopwatch.Stop();
            var generationTime = stopwatch.ElapsedMilliseconds;
            
            Assert.Less(generationTime, 200, $"Medium chunk (16�) took too long to generate: {generationTime}ms");
            UnityEngine.Debug.Log($"Medium chunk generation time: {generationTime}ms");
        }

        [UnityTest]
        public IEnumerator LargeChunk_ShouldGenerateWithinReasonableTime()
        {
            // Arrange
            _voxelChunk.chunkSize = 32;
            var stopwatch = Stopwatch.StartNew();

            // Act
            yield return null; // Wait for generation

            // Assert
            stopwatch.Stop();
            var generationTime = stopwatch.ElapsedMilliseconds;
            
            Assert.Less(generationTime, 1000, $"Large chunk (32�) took too long to generate: {generationTime}ms");
            UnityEngine.Debug.Log($"Large chunk generation time: {generationTime}ms");
        }

        #endregion

        #region Memory Performance Tests

        [UnityTest]
        public IEnumerator MeshGeneration_ShouldNotExcessivelyAllocateMemory()
        {
            // Arrange
            _voxelChunk.chunkSize = 16;
            
            // Force garbage collection before test
            System.GC.Collect();
            System.GC.WaitForPendingFinalizers();
            yield return null;

            // Record initial memory
            Profiler.BeginSample("VoxelChunk Memory Test");
            var initialMemory = Profiler.GetTotalAllocatedMemoryLong();

            // Act
            yield return null; // Generate chunk

            // Measure memory after generation
            var afterGenerationMemory = Profiler.GetTotalAllocatedMemoryLong();
            Profiler.EndSample();

            // Assert
            var memoryUsed = afterGenerationMemory - initialMemory;
            var memoryUsedMB = memoryUsed / (1024f * 1024f);
            
            Assert.Less(memoryUsedMB, 10f, $"Chunk generation used too much memory: {memoryUsedMB:F2}MB");
            UnityEngine.Debug.Log($"Memory used for chunk generation: {memoryUsedMB:F2}MB");
        }

        [UnityTest]
        public IEnumerator MultipleRegenerations_ShouldNotLeakMemory()
        {
            // Arrange
            _voxelChunk.chunkSize = 8;
            yield return null; // Initial generation

            // Force garbage collection
            System.GC.Collect();
            System.GC.WaitForPendingFinalizers();
            yield return null;

            var initialMemory = Profiler.GetTotalAllocatedMemoryLong();

            // Act - Perform multiple regenerations
            for (int i = 0; i < 5; i++)
            {
                _voxelChunk.RegenerateChunk();
                yield return null;
            }

            // Force garbage collection after regenerations
            System.GC.Collect();
            System.GC.WaitForPendingFinalizers();
            yield return null;

            // Assert
            var finalMemory = Profiler.GetTotalAllocatedMemoryLong();
            var memoryDifference = finalMemory - initialMemory;
            var memoryDifferenceMB = memoryDifference / (1024f * 1024f);
            
            Assert.Less(memoryDifferenceMB, 5f, 
                $"Multiple regenerations leaked memory: {memoryDifferenceMB:F2}MB");
            UnityEngine.Debug.Log($"Memory difference after regenerations: {memoryDifferenceMB:F2}MB");
        }

        #endregion

        #region Face Culling Performance Tests

        [UnityTest]
        public IEnumerator FaceCulling_ShouldSignificantlyReduceTriangleCount()
        {
            // Arrange - Create two scenarios: isolated voxels vs connected voxels
            _voxelChunk.chunkSize = 8;
            
            // Scenario 1: All voxels isolated (worst case for face culling)
            for (int x = 0; x < 8; x += 2)
            {
                for (int y = 0; y < 8; y += 2)
                {
                    for (int z = 0; z < 8; z += 2)
                    {
                        _voxelChunk.SetVoxel(x, y, z, true);
                    }
                }
            }
            
            _voxelChunk.RegenerateChunk();
            yield return null;
            
            var isolatedMesh = _voxelChunk.GetComponent<MeshFilter>().mesh;
            var isolatedTriangleCount = isolatedMesh.triangles.Length / 3;

            // Scenario 2: All voxels connected (best case for face culling)
            for (int x = 0; x < 8; x++)
            {
                for (int y = 0; y < 8; y++)
                {
                    for (int z = 0; z < 8; z++)
                    {
                        _voxelChunk.SetVoxel(x, y, z, true);
                    }
                }
            }
            
            _voxelChunk.RegenerateChunk();
            yield return null;
            
            var connectedMesh = _voxelChunk.GetComponent<MeshFilter>().mesh;
            var connectedTriangleCount = connectedMesh.triangles.Length / 3;

            // Assert - Connected voxels should have significantly fewer triangles
            var reductionRatio = (float)connectedTriangleCount / isolatedTriangleCount;
            
            Assert.Less(reductionRatio, 0.5f, 
                $"Face culling not effective enough. Isolated: {isolatedTriangleCount}, Connected: {connectedTriangleCount}, Ratio: {reductionRatio:F2}");
            
            UnityEngine.Debug.Log($"Face culling effectiveness - Isolated: {isolatedTriangleCount} triangles, Connected: {connectedTriangleCount} triangles, Reduction: {(1-reductionRatio)*100:F1}%");
        }

        #endregion

        #region Scalability Tests

        [UnityTest]
        public IEnumerator GenerationTime_ShouldScaleReasonablyWithSize()
        {
            // Test different chunk sizes and measure generation time
            var sizes = new[] { 4, 8, 16 };
            var times = new float[sizes.Length];

            for (int i = 0; i < sizes.Length; i++)
            {
                // Create new chunk for each size test
                if (_testGameObject != null)
                    Object.Destroy(_testGameObject);
                
                _testGameObject = new GameObject($"ScalabilityTest_{sizes[i]}");
                _voxelChunk = _testGameObject.AddComponent<VoxelChunk>();
                _voxelChunk.chunkSize = sizes[i];
                _voxelChunk.showDebugInfo = false;

                var stopwatch = Stopwatch.StartNew();
                yield return null; // Wait for generation
                stopwatch.Stop();
                
                times[i] = stopwatch.ElapsedMilliseconds;
                UnityEngine.Debug.Log($"Chunk size {sizes[i]}�: {times[i]}ms");
            }

            // Assert - Time should not increase exponentially
            // From 4 to 8 (8x volume increase) should not be more than 16x time increase
            var timeRatio_4to8 = times[1] / times[0];
            Assert.Less(timeRatio_4to8, 16f, $"Time scaling from 4� to 8� too steep: {timeRatio_4to8:F1}x");

            // From 8 to 16 (8x volume increase) should not be more than 16x time increase
            var timeRatio_8to16 = times[2] / times[1];
            Assert.Less(timeRatio_8to16, 16f, $"Time scaling from 8� to 16� too steep: {timeRatio_8to16:F1}x");
        }

        #endregion

        #region Regression Performance Tests

        [UnityTest]
        public IEnumerator PerformanceRegression_ShouldMaintainBaselinePerformance()
        {
            // Baseline performance test - if this fails, performance has regressed
            _voxelChunk.chunkSize = 16;
            
            var stopwatch = Stopwatch.StartNew();
            yield return null; // Generate chunk
            stopwatch.Stop();
            
            var generationTime = stopwatch.ElapsedMilliseconds;
            
            // Baseline: 16� chunk should generate in under 200ms on reasonable hardware
            Assert.Less(generationTime, 200, 
                $"Performance regression detected! 16� chunk took {generationTime}ms (baseline: <200ms)");
            
            // Also check triangle count efficiency
            var mesh = _voxelChunk.GetComponent<MeshFilter>().mesh;
            var triangleCount = mesh.triangles.Length / 3;
            var voxelCount = 16 * 16 * 16;
            var trianglesPerVoxel = (float)triangleCount / voxelCount;
            
            // With good face culling, should average much less than 12 triangles per voxel
            Assert.Less(trianglesPerVoxel, 8f, 
                $"Face culling regression detected! {trianglesPerVoxel:F1} triangles per voxel (baseline: <8)");
            
            UnityEngine.Debug.Log($"Performance baseline check - Time: {generationTime}ms, Triangles/Voxel: {trianglesPerVoxel:F1}");
        }

        #endregion

        #region Stress Tests

        [UnityTest]
        public IEnumerator StressTest_MultipleChunksSimultaneously()
        {
            // Create multiple chunks to test system under load
            var chunks = new GameObject[4];
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Create 4 chunks simultaneously
                for (int i = 0; i < chunks.Length; i++)
                {
                    chunks[i] = new GameObject($"StressTestChunk_{i}");
                    var chunk = chunks[i].AddComponent<VoxelChunk>();
                    chunk.chunkSize = 8; // Smaller size for stress test
                    chunk.showDebugInfo = false;
                }

                yield return null; // Wait for all to generate

                stopwatch.Stop();
                var totalTime = stopwatch.ElapsedMilliseconds;

                // Assert - Multiple chunks shouldn't take excessively long
                Assert.Less(totalTime, 500, $"Multiple chunk generation took too long: {totalTime}ms");
                
                // Verify all chunks generated successfully
                for (int i = 0; i < chunks.Length; i++)
                {
                    var mesh = chunks[i].GetComponent<MeshFilter>().mesh;
                    Assert.IsNotNull(mesh, $"Chunk {i} failed to generate mesh");
                    Assert.Greater(mesh.vertices.Length, 0, $"Chunk {i} has no vertices");
                }

                UnityEngine.Debug.Log($"Stress test completed - 4 chunks in {totalTime}ms");
            }
            finally
            {
                // Cleanup
                for (int i = 0; i < chunks.Length; i++)
                {
                    if (chunks[i] != null)
                        Object.Destroy(chunks[i]);
                }
            }
        }

        #endregion
    }
}
