<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="34" result="Failed(Child)" total="34" passed="5" failed="29" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:28Z" duration="0,6117212">
  <test-suite type="TestSuite" id="1000" name="Excidium" fullname="Excidium" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:28Z" duration="0.611721" total="34" passed="5" failed="29" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1038" name="ExcidiumTests.dll" fullname="F:/Unity/Projects/Excidium/Library/ScriptAssemblies/ExcidiumTests.dll" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:28Z" duration="0.607953" total="34" passed="5" failed="29" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="18932" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1039" name="ExcidiumTests" fullname="ExcidiumTests" runstate="Runnable" testcasecount="34" result="Failed" site="Child" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:28Z" duration="0.607761" total="34" passed="5" failed="29" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestFixture" id="1001" name="VoxelChunkIntegrationTests" fullname="ExcidiumTests.VoxelChunkIntegrationTests" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" testcasecount="12" result="Failed" site="Child" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.036646" total="12" passed="0" failed="12" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1011" name="ChunkBoundaryVoxels_ShouldRenderCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.ChunkBoundaryVoxels_ShouldRenderCorrectly" methodname="ChunkBoundaryVoxels_ShouldRenderCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1961513014" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.009952" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1013" name="DebugValidation_WhenEnabled_ShouldNotThrowErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.DebugValidation_WhenEnabled_ShouldNotThrowErrors" methodname="DebugValidation_WhenEnabled_ShouldNotThrowErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1422338490" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.002877" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1009" name="EmptyChunk_ShouldHandleGracefully" fullname="ExcidiumTests.VoxelChunkIntegrationTests.EmptyChunk_ShouldHandleGracefully" methodname="EmptyChunk_ShouldHandleGracefully" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="2078840356" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000843" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1010" name="FullChunk_ShouldOnlyRenderExteriorFaces" fullname="ExcidiumTests.VoxelChunkIntegrationTests.FullChunk_ShouldOnlyRenderExteriorFaces" methodname="FullChunk_ShouldOnlyRenderExteriorFaces" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1310081955" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000769" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1005" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkIntegrationTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1647490398" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.003458" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[UnityEngine.MissingComponentException : There is no 'MeshFilter' attached to the "TestVoxelChunk" game object, but a script is trying to access it.
You probably need to add a MeshFilter to the game object "TestVoxelChunk". Or your script needs to check if the component is attached before using it.
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1004" name="MeshCollider_ShouldBeUpdatedWithMesh" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshCollider_ShouldBeUpdatedWithMesh" methodname="MeshCollider_ShouldBeUpdatedWithMesh" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="733705600" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.001102" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[UnityEngine.MissingComponentException : There is no 'MeshCollider' attached to the "TestVoxelChunk" game object, but a script is trying to access it.
You probably need to add a MeshCollider to the game object "TestVoxelChunk". Or your script needs to check if the component is attached before using it.
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1008" name="MeshNormals_ShouldBeCalculatedCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshNormals_ShouldBeCalculatedCorrectly" methodname="MeshNormals_ShouldBeCalculatedCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="638076336" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000909" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[UnityEngine.MissingComponentException : There is no 'MeshFilter' attached to the "TestVoxelChunk" game object, but a script is trying to access it.
You probably need to add a MeshFilter to the game object "TestVoxelChunk". Or your script needs to check if the component is attached before using it.
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1007" name="MeshRenderer_ShouldHaveMaterialAssigned" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MeshRenderer_ShouldHaveMaterialAssigned" methodname="MeshRenderer_ShouldHaveMaterialAssigned" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1753244868" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.003861" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[UnityEngine.MissingComponentException : There is no 'MeshRenderer' attached to the "TestVoxelChunk" game object, but a script is trying to access it.
You probably need to add a MeshRenderer to the game object "TestVoxelChunk". Or your script needs to check if the component is attached before using it.
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1006" name="MultipleRegenerations_ShouldNotCausePerformanceDegradation" fullname="ExcidiumTests.VoxelChunkIntegrationTests.MultipleRegenerations_ShouldNotCausePerformanceDegradation" methodname="MultipleRegenerations_ShouldNotCausePerformanceDegradation" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="1874672339" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.001917" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1012" name="RapidModifications_ShouldNotCauseErrors" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RapidModifications_ShouldNotCauseErrors" methodname="RapidModifications_ShouldNotCauseErrors" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="451455661" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000901" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1003" name="RuntimeModification_ShouldUpdateMeshCorrectly" fullname="ExcidiumTests.VoxelChunkIntegrationTests.RuntimeModification_ShouldUpdateMeshCorrectly" methodname="RuntimeModification_ShouldUpdateMeshCorrectly" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="613522555" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000746" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[UnityEngine.MissingComponentException : There is no 'MeshFilter' attached to the "TestVoxelChunk" game object, but a script is trying to access it.
You probably need to add a MeshFilter to the game object "TestVoxelChunk". Or your script needs to check if the component is attached before using it.
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1002" name="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" fullname="ExcidiumTests.VoxelChunkIntegrationTests.VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" methodname="VoxelChunk_WhenStarted_ShouldGenerateMeshInOneFrame" classname="ExcidiumTests.VoxelChunkIntegrationTests" runstate="Runnable" seed="2080313616" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000675" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[UnityEngine.MissingComponentException : There is no 'MeshFilter' attached to the "TestVoxelChunk" game object, but a script is trying to access it.
You probably need to add a MeshFilter to the game object "TestVoxelChunk". Or your script needs to check if the component is attached before using it.
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkIntegrationTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkIntegrationTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1014" name="VoxelChunkPerformanceTests" fullname="ExcidiumTests.VoxelChunkPerformanceTests" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" testcasecount="9" result="Failed" site="Child" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.368089" total="9" passed="0" failed="9" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1020" name="FaceCulling_ShouldSignificantlyReduceTriangleCount" fullname="ExcidiumTests.VoxelChunkPerformanceTests.FaceCulling_ShouldSignificantlyReduceTriangleCount" methodname="FaceCulling_ShouldSignificantlyReduceTriangleCount" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1082597409" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000851" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1021" name="GenerationTime_ShouldScaleReasonablyWithSize" fullname="ExcidiumTests.VoxelChunkPerformanceTests.GenerationTime_ShouldScaleReasonablyWithSize" methodname="GenerationTime_ShouldScaleReasonablyWithSize" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="306952910" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.001119" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1017" name="LargeChunk_ShouldGenerateWithinReasonableTime" fullname="ExcidiumTests.VoxelChunkPerformanceTests.LargeChunk_ShouldGenerateWithinReasonableTime" methodname="LargeChunk_ShouldGenerateWithinReasonableTime" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1244554897" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.001518" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Large chunk generation time: 0ms
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1016" name="MediumChunk_ShouldGenerateWithinBudget" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MediumChunk_ShouldGenerateWithinBudget" methodname="MediumChunk_ShouldGenerateWithinBudget" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1105771930" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000803" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Medium chunk generation time: 0ms
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1018" name="MeshGeneration_ShouldNotExcessivelyAllocateMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MeshGeneration_ShouldNotExcessivelyAllocateMemory" methodname="MeshGeneration_ShouldNotExcessivelyAllocateMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1062722186" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.183497" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Memory used for chunk generation: 0,00MB
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1019" name="MultipleRegenerations_ShouldNotLeakMemory" fullname="ExcidiumTests.VoxelChunkPerformanceTests.MultipleRegenerations_ShouldNotLeakMemory" methodname="MultipleRegenerations_ShouldNotLeakMemory" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="331142937" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.175105" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1022" name="PerformanceRegression_ShouldMaintainBaselinePerformance" fullname="ExcidiumTests.VoxelChunkPerformanceTests.PerformanceRegression_ShouldMaintainBaselinePerformance" methodname="PerformanceRegression_ShouldMaintainBaselinePerformance" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1317213390" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.001291" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[UnityEngine.MissingComponentException : There is no 'MeshFilter' attached to the "PerformanceTestChunk" game object, but a script is trying to access it.
You probably need to add a MeshFilter to the game object "PerformanceTestChunk". Or your script needs to check if the component is attached before using it.
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1015" name="SmallChunk_ShouldGenerateQuickly" fullname="ExcidiumTests.VoxelChunkPerformanceTests.SmallChunk_ShouldGenerateQuickly" methodname="SmallChunk_ShouldGenerateQuickly" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="1872931467" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000937" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Small chunk generation time: 0ms
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
          <test-case id="1023" name="StressTest_MultipleChunksSimultaneously" fullname="ExcidiumTests.VoxelChunkPerformanceTests.StressTest_MultipleChunksSimultaneously" methodname="StressTest_MultipleChunksSimultaneously" classname="ExcidiumTests.VoxelChunkPerformanceTests" runstate="Runnable" seed="916204524" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.002067" asserts="0">
            <properties>
              <property name="_JOINTYPE" value="UnityCombinatorial" />
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect
TearDown : Unhandled log message: '[Error] Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.Object:Destroy (UnityEngine.Object)
ExcidiumTests.VoxelChunkPerformanceTests:TearDown () (at Assets/ExcidiumTests/VoxelChunkPerformanceTests.cs:33)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
Destroy may not be called from edit mode! Use DestroyImmediate instead.
Destroying an object in edit mode destroys it permanently.
]]></output>
          </test-case>
        </test-suite>
        <test-suite type="TestFixture" id="1024" name="VoxelChunkTests" fullname="ExcidiumTests.VoxelChunkTests" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" testcasecount="13" result="Failed" site="Child" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:28Z" duration="0.202167" total="13" passed="5" failed="8" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-case id="1034" name="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" fullname="ExcidiumTests.VoxelChunkTests.FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" methodname="FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1961081349" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.008038" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Adjacent voxels should have 40 vertices (10 faces), got 240
  Expected: 40
  But was:  240
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.FaceCulling_AdjacentVoxels_ShouldNotGenerateSharedFaces () [0x00088] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:320
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1032" name="FaceGeneration_ShouldHaveCorrectWindingOrder" fullname="ExcidiumTests.VoxelChunkTests.FaceGeneration_ShouldHaveCorrectWindingOrder" methodname="FaceGeneration_ShouldHaveCorrectWindingOrder" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="625282814" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.001504" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[Unhandled log message: '[Error] Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:FaceGeneration_ShouldHaveCorrectWindingOrder () (at Assets/ExcidiumTests/VoxelChunkTests.cs:215)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1027" name="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" fullname="ExcidiumTests.VoxelChunkTests.GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" methodname="GetVoxel_WithValidCoordinates_ShouldReturnCorrectValue" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1596251313" result="Passed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000536" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1036" name="MeshBounds_ShouldMatchChunkSize" fullname="ExcidiumTests.VoxelChunkTests.MeshBounds_ShouldMatchChunkSize" methodname="MeshBounds_ShouldMatchChunkSize" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="255001860" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.001099" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Bounds should include chunk max Y
  Expected: greater than or equal to 4.0f
  But was:  2.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.MeshBounds_ShouldMatchChunkSize () [0x000a1] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:363
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1030" name="MeshGeneration_WhenCalled_ShouldCreateValidMesh" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WhenCalled_ShouldCreateValidMesh" methodname="MeshGeneration_WhenCalled_ShouldCreateValidMesh" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1236680320" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.001557" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[Unhandled log message: '[Error] Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:MeshGeneration_WhenCalled_ShouldCreateValidMesh () (at Assets/ExcidiumTests/VoxelChunkTests.cs:148)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1031" name="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" fullname="ExcidiumTests.VoxelChunkTests.MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" methodname="MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="172645996" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000720" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Single voxel should have 24 vertices (6 faces � 4 vertices)
  Expected: 24
  But was:  240
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.MeshGeneration_WithSingleVoxel_ShouldCreateCorrectFaceCount () [0x00069] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:184
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1035" name="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" fullname="ExcidiumTests.VoxelChunkTests.RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" methodname="RegenerateChunk_ShouldProduceSameResultAsInitialGeneration" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1460424419" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000941" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[Unhandled log message: '[Error] Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.'. Use UnityEngine.TestTools.LogAssert.Expect]]></message>
              <stack-trace><![CDATA[UnityEngine.MeshFilter:get_mesh ()
ExcidiumTests.VoxelChunkTests:RegenerateChunk_ShouldProduceSameResultAsInitialGeneration () (at Assets/ExcidiumTests/VoxelChunkTests.cs:335)
System.Reflection.MethodBase:Invoke (object,object[])
NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object,object[])
NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
NUnit.Framework.Internal.Commands.TestMethodCommand:RunNonAsyncTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:RunTestMethod (NUnit.Framework.Internal.ITestExecutionContext)
NUnit.Framework.Internal.Commands.TestMethodCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1029" name="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithInvalidCoordinates_ShouldNotCrash" methodname="SetVoxel_WithInvalidCoordinates_ShouldNotCrash" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1948276674" result="Passed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000780" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1028" name="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" fullname="ExcidiumTests.VoxelChunkTests.SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" methodname="SetVoxel_WithValidCoordinates_ShouldUpdateVoxelState" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="249136348" result="Passed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000325" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1033" name="TopFace_ShouldPointUpward" fullname="ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward" methodname="TopFace_ShouldPointUpward" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="870621044" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:27Z" duration="0.000969" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Top face normal should point upward. Normal: (0.00, 0.00, 1.00)
  Expected: greater than 0.800000012f
  But was:  0.0f
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.TopFace_ShouldPointUpward () [0x000af] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:277
]]></stack-trace>
            </failure>
            <output><![CDATA[Instantiating mesh due to calling MeshFilter.mesh during edit mode. This will leak meshes. Please use MeshFilter.sharedMesh instead.
]]></output>
          </test-case>
          <test-case id="1037" name="VoxelChunk_ShouldNotHaveMemoryLeaks" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_ShouldNotHaveMemoryLeaks" methodname="VoxelChunk_ShouldNotHaveMemoryLeaks" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="400641539" result="Failed" start-time="2025-07-03 07:49:27Z" end-time="2025-07-03 07:49:28Z" duration="0.182874" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <failure>
              <message><![CDATA[  Should not leak meshes. Initial: 24, Final: 29, Difference: 5
  Expected: less than or equal to 2
  But was:  5
]]></message>
              <stack-trace><![CDATA[at ExcidiumTests.VoxelChunkTests.VoxelChunk_ShouldNotHaveMemoryLeaks () [0x00056] in F:\Unity\Projects\Excidium\Assets\ExcidiumTests\VoxelChunkTests.cs:391
]]></stack-trace>
            </failure>
          </test-case>
          <test-case id="1025" name="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" methodname="VoxelChunk_WhenCreated_ShouldHaveRequiredComponents" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1794481044" result="Passed" start-time="2025-07-03 07:49:28Z" end-time="2025-07-03 07:49:28Z" duration="0.000659" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
          </test-case>
          <test-case id="1026" name="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" fullname="ExcidiumTests.VoxelChunkTests.VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" methodname="VoxelChunk_WhenInitialized_ShouldGenerateVoxelArray" classname="ExcidiumTests.VoxelChunkTests" runstate="Runnable" seed="1151044822" result="Passed" start-time="2025-07-03 07:49:28Z" end-time="2025-07-03 07:49:28Z" duration="0.000488" asserts="0">
            <properties>
              <property name="retryIteration" value="0" />
              <property name="repeatIteration" value="0" />
            </properties>
            <output><![CDATA[Saving results to: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/Excidium\TestResults.xml
]]></output>
          </test-case>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>